@value variables: "@styles/variables.module.css";
@value brandColorOne, brandColorTwo, brandColorThree, brandColorFour, brandColorFive, colorWhite, colorBlack, gray300 from variables;
@value breakpoints: "@styles/breakpoints.module.css";
@value breakpoint-sm, breakpoint-xl, breakpoint-xl-1024, breakpoint-xl-1440 from breakpoints;

.questions_container {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.question_wrapper {
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding: 24px;
  border: 2px solid #e9ecef;
  border-radius: 12px;
  background-color: colorWhite;
  transition: all 0.3s ease;
}

.question_wrapper.error {
  border-color: #ff4444;
  background-color: #fff5f5;
}

.question_header {
  display: flex;
  gap: 16px;
  align-items: flex-start;
}

.question_number {
  background-color: brandColorOne;
  color: colorWhite;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 16px;
  flex-shrink: 0;
}

.question_number.error_number {
  background-color: #ff4444;
}

.question_name {
  font-weight: 600;
  font-size: 18px;
  line-height: 1.4;
  color: colorBlack;
  flex: 1;
}

.mcqs_container {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-left: 56px;

  @media screen and (max-width: breakpoint-xl-1024) {
    margin-left: 0;
  }
}

.mcq {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  background-color: colorWhite;
  font-size: 16px;
  line-height: 1.5;
}

.mcq:hover {
  border-color: brandColorOne;
  background-color: #f8f9ff;
}

.mcq.selected_mcq {
  border-color: brandColorOne;
  background-color: #f0f4ff;
  color: brandColorOne;
  font-weight: 600;
}

.mcq input[type="radio"] {
  width: 20px;
  height: 20px;
  accent-color: brandColorOne;
}

.sub_question_container {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid brandColorOne;
}

.sub_question_title {
  font-weight: 600;
  font-size: 16px;
  color: colorBlack;
  margin-bottom: 8px;
}

.sub_question_answers {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.input_container,
.select_container {
  margin-left: 56px;

  @media screen and (max-width: breakpoint-xl-1024) {
    margin-left: 0;
  }
}

.text_input,
.number_input,
.select_input {
  width: 100%;
  padding: 16px;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  font-size: 16px;
  line-height: 1.5;
  transition: all 0.3s ease;
  background-color: colorWhite;
}

.text_input:focus,
.number_input:focus,
.select_input:focus {
  outline: none;
  border-color: brandColorOne;
  box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.1);
}

.number_input {
  max-width: 200px;
}

.select_input {
  cursor: pointer;
}

.select_input option {
  padding: 8px;
}

/* Responsive adjustments */
@media screen and (max-width: breakpoint-xl-1024) {
  .question_wrapper {
    padding: 16px;
  }
  
  .question_header {
    gap: 12px;
  }
  
  .question_number {
    width: 32px;
    height: 32px;
    font-size: 14px;
  }
  
  .question_name {
    font-size: 16px;
  }
  
  .mcq {
    padding: 12px;
    font-size: 14px;
  }
  
  .sub_question_container {
    padding: 16px;
  }
  
  .text_input,
  .number_input,
  .select_input {
    padding: 12px;
    font-size: 14px;
  }
}

/* Animation for error state */
.question_wrapper.error {
  animation: shake 0.5s ease-in-out;
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-5px); }
  75% { transform: translateX(5px); }
}

'use client';

import { useState } from 'react';
import Image from 'next/image';
import styles from './CloudMigrationForm.module.css';
import useForm from '@hooks/useForm';
import Button from '@components/Button';
import getUserLocation from '@utils/getUserLocation';

export default function CloudMigrationForm({
  formData,
  source = 'CloudMigration',
  handleResult,
  handleVisibleSection,
}: any) {
  const {
    title,
    instructions,
    consent_statement,
    LinkedInButton_title,
    button,
    formFields: {
      fieldNameFor_FirstName,
      fieldNameFor_LastName,
      fieldNameFor_EmailAddress,
      fieldNameFor_CompanyName,
      fieldNameFor_PhoneNumber,
      fieldNameFor_HowCanWeHelpYou,
    },
  } = formData;

  const [isSubmitting, setIsSubmitting] = useState(false);
  const userCountryCode = getUserLocation();

  const {
    values,
    errors,
    errorMessages,
    handleChange,
    handleSubmitCloudMigration,
  } = useForm(
    {
      firstName: '',
      lastName: '',
      emailAddress: '',
      phoneNumber: '',
      howDidYouHearAboutUs: '',
      companyName: '',
      howCanWeHelpYou: '',
      consent: false,
    },
    {
      firstName: {
        empty: false,
      },
      lastName: {
        empty: false,
      },
      emailAddress: {
        empty: false,
        invalid: false,
      },
      phoneNumber: {
        empty: false,
        invalid: false,
      },
      consent: {
        empty: false,
      },
    },
    'default',
    source,
  );

  const onSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();

    setIsSubmitting(true);

    try {
      let { data, calculationResult } = handleResult();

      await handleSubmitCloudMigration(data, calculationResult, handleVisibleSection);
    } catch (error) {
      console.error('Form submission failed:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className={styles.form_container}>
      <div className={styles.form_header}>
        <h3>{title || 'Get Your Detailed Migration Plan'}</h3>
        {instructions && (
          <p className={styles.instructions}>{instructions}</p>
        )}
      </div>

      <form onSubmit={onSubmit} className={styles.form}>
        <div className={styles.form_row}>
          <div className={styles.form_group}>
            <label htmlFor="firstName" className={styles.label}>
              {fieldNameFor_FirstName || 'First Name'} *
            </label>
            <input
              type="text"
              id="firstName"
              name="firstName"
              value={values.firstName}
              onChange={e => handleChange(e.target)}
              className={
                errors.firstName?.empty
                  ? `${styles.input} ${styles.error}`
                  : styles.input
              }
              placeholder="Enter your first name"
            />
          </div>

          <div className={styles.form_group}>
            <label htmlFor="lastName" className={styles.label}>
              {fieldNameFor_LastName || 'Last Name'} *
            </label>
            <input
              type="text"
              id="lastName"
              name="lastName"
              value={values.lastName}
              onChange={e => handleChange(e.target)}
              className={
                errors.lastName?.empty
                  ? `${styles.input} ${styles.error}`
                  : styles.input
              }
              placeholder="Enter your last name"
            />
          </div>
        </div>

        <div className={styles.form_row}>
          <div className={styles.form_group}>
            <label htmlFor="emailAddress" className={styles.label}>
              {fieldNameFor_EmailAddress || 'Email Address'} *
            </label>
            <input
              type="email"
              id="emailAddress"
              name="emailAddress"
              value={values.emailAddress}
              onChange={e => handleChange(e.target)}
              className={
                errors.emailAddress?.empty || errors.emailAddress?.invalid
                  ? `${styles.input} ${styles.error}`
                  : styles.input
              }
              placeholder="Enter your email address"
            />
          </div>

          <div className={styles.form_group}>
            <label htmlFor="phoneNumber" className={styles.label}>
              {fieldNameFor_PhoneNumber || 'Phone Number'} *
            </label>
            <input
              type="tel"
              id="phoneNumber"
              name="phoneNumber"
              value={values.phoneNumber}
              onChange={e => handleChange(e.target)}
              className={
                errors.phoneNumber?.empty || errors.phoneNumber?.invalid
                  ? `${styles.input} ${styles.error}`
                  : styles.input
              }
              placeholder={`+${userCountryCode} Enter your phone number`}
            />
          </div>
        </div>

        <div className={styles.form_group}>
          <label htmlFor="companyName" className={styles.label}>
            {fieldNameFor_CompanyName || 'Company Name'}
          </label>
          <input
            type="text"
            id="companyName"
            name="companyName"
            value={values.companyName}
            onChange={e => handleChange(e.target)}
            className={styles.input}
            placeholder="Enter your company name"
          />
        </div>

        <div className={styles.form_group}>
          <label htmlFor="howCanWeHelpYou" className={styles.label}>
            {fieldNameFor_HowCanWeHelpYou || 'How can we help you with your cloud migration?'}
          </label>
          <textarea
            id="howCanWeHelpYou"
            name="howCanWeHelpYou"
            value={values.howCanWeHelpYou}
            onChange={e => handleChange(e.target)}
            className={styles.textarea}
            placeholder="Tell us about your specific cloud migration needs..."
            rows={4}
          />
        </div>

        {(errorMessages.empty || errorMessages.invalid) && (
          <div className={styles.error_message}>
            {errorMessages.empty && <p>{errorMessages.empty}</p>}
            {errorMessages.invalid && <p>{errorMessages.invalid}</p>}
          </div>
        )}

        <div className={styles.consent_row}>
          <label
            className={
              errors.consent?.empty
                ? `${styles.consent_label} ${styles.error}`
                : styles.consent_label
            }
            onChange={e => handleChange(e.target)}
          >
            <input
              type="checkbox"
              id="consent"
              name="consent"
              checked={values.consent}
            />
            <span>{consent_statement || 'I agree to receive communications about cloud migration services.'}</span>
          </label>
        </div>

        <div className={styles.submit_button_row}>
          {isSubmitting ? (
            <div className={styles.container_spinner}>
              <div className={styles.spinner}></div>
            </div>
          ) : (
            <Button
              type="submit"
              className={styles.submit_button}
              label="Get My Detailed Migration Plan"
            />
          )}

          {LinkedInButton_title && (
            <a className={styles.linkedin_button} href="#">
              {LinkedInButton_title}
              <Image
                src="https://cdn.marutitech.com/linkedin_c13ca9a536.png"
                width={32}
                height={32}
                alt="LinkedIn Logo"
              />
            </a>
          )}
        </div>
      </form>
    </div>
  );
}

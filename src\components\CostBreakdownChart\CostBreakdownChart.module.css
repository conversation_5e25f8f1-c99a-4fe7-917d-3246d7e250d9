@value variables: "@styles/variables.module.css";
@value brandColorOne, brandColorTwo, brandColorThree, brandColorFour, brandColorFive, colorWhite, colorBlack, gray300 from variables;
@value breakpoints: "@styles/breakpoints.module.css";
@value breakpoint-sm, breakpoint-xl, breakpoint-xl-1024, breakpoint-xl-1440 from breakpoints;

.chart_container {
  background-color: colorWhite;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.tab_navigation {
  display: flex;
  border-bottom: 2px solid #e9ecef;
}

.tab {
  flex: 1;
  padding: 16px 24px;
  background-color: transparent;
  border: none;
  font-size: 16px;
  font-weight: 600;
  color: #666;
  cursor: pointer;
  transition: all 0.3s ease;
}

.tab:hover {
  background-color: #f8f9fa;
  color: brandColorOne;
}

.tab.active {
  background-color: brandColorOne;
  color: colorWhite;
}

.breakdown_content,
.comparison_content {
  padding: 32px;
}

.total_cost_display {
  text-align: center;
  margin-bottom: 32px;
  padding: 24px;
  background: linear-gradient(135deg, #f8f9ff 0%, #e8f2ff 100%);
  border-radius: 12px;
}

.total_cost_display h3 {
  margin: 0 0 12px 0;
  font-size: 18px;
  color: #666;
  font-weight: 500;
}

.total_amount {
  font-size: 36px;
  font-weight: 700;
  color: brandColorOne;
  margin: 0;
}

.breakdown_chart {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.chart_visual {
  display: flex;
  height: 40px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.chart_segment {
  height: 100%;
  transition: all 0.3s ease;
  cursor: pointer;
}

.chart_segment:hover {
  opacity: 0.8;
  transform: scaleY(1.1);
}

.chart_legend {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 16px;
}

.legend_item {
  display: flex;
  gap: 12px;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.legend_item:hover {
  background-color: #e9ecef;
  transform: translateY(-2px);
}

.legend_color {
  width: 16px;
  height: 16px;
  border-radius: 4px;
  flex-shrink: 0;
  margin-top: 2px;
}

.legend_details {
  flex: 1;
}

.legend_label {
  font-weight: 600;
  font-size: 14px;
  color: colorBlack;
  margin-bottom: 4px;
}

.legend_value {
  font-weight: 700;
  font-size: 16px;
  color: brandColorOne;
  margin-bottom: 4px;
}

.legend_description {
  font-size: 12px;
  color: #666;
  line-height: 1.4;
}

.comparison_content h3 {
  text-align: center;
  margin-bottom: 24px;
  font-size: 24px;
  color: colorBlack;
}

.provider_grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.provider_card {
  position: relative;
  padding: 24px;
  border: 2px solid #e9ecef;
  border-radius: 12px;
  background-color: colorWhite;
  transition: all 0.3s ease;
}

.provider_card:hover {
  border-color: brandColorOne;
  box-shadow: 0 4px 16px rgba(74, 144, 226, 0.1);
  transform: translateY(-2px);
}

.provider_card.recommended {
  border-color: brandColorOne;
  background: linear-gradient(135deg, #f8f9ff 0%, #ffffff 100%);
  box-shadow: 0 4px 16px rgba(74, 144, 226, 0.15);
}

.recommended_badge {
  position: absolute;
  top: -10px;
  right: 16px;
  background-color: brandColorOne;
  color: colorWhite;
  padding: 6px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
}

.provider_header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.provider_header h4 {
  margin: 0;
  font-size: 20px;
  font-weight: 700;
  color: colorBlack;
}

.provider_cost {
  font-size: 18px;
  font-weight: 700;
  color: brandColorOne;
}

.provider_savings {
  font-size: 14px;
  color: #28a745;
  font-weight: 600;
  margin-bottom: 16px;
}

.provider_features h5 {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 600;
  color: colorBlack;
}

.provider_features ul {
  margin: 0;
  padding-left: 16px;
  list-style-type: disc;
}

.provider_features li {
  font-size: 13px;
  color: #666;
  margin-bottom: 4px;
  line-height: 1.4;
}

@media screen and (max-width: breakpoint-xl-1024) {
  .breakdown_content,
  .comparison_content {
    padding: 20px;
  }
  
  .total_cost_display {
    padding: 16px;
  }
  
  .total_amount {
    font-size: 28px;
  }
  
  .chart_legend {
    grid-template-columns: 1fr;
  }
  
  .provider_grid {
    grid-template-columns: 1fr;
  }
  
  .tab {
    padding: 12px 16px;
    font-size: 14px;
  }
}

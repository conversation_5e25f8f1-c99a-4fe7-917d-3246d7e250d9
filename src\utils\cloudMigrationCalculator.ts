// Cloud Migration Cost Calculator
// Based on the assessment data structure from the spreadsheet

export interface MigrationAssessmentData {
  businessInfrastructure: {
    elementsToMigrate: string;
    numberOfServers: number;
    dataType: string;
    currentInfrastructure: string;
    serverCapacity: number;
    currentMonthlyCost: number;
    migrationPurpose: string;
  };
  workloadAnalysis: {
    workloadTypes: string[];
    averageCpuUsage: number;
    highAvailabilityRequired: boolean;
    cloudProvider: string;
    pricingModel: string;
  };
  cloudPreferences: {
    targetEnvironments: string[];
    complianceRequirements: string[];
    migrationStrategy: string;
    autoScaling: boolean;
    reviewFrequency: string;
  };
}

export interface CostCalculationResult {
  summary: {
    totalCost: number;
    monthlyOperationalCost: number;
    migrationTimeframe: string;
    recommendedProvider: string;
    costRange: {
      lower: number;
      upper: number;
    };
  };
  breakdown: {
    infrastructure: number;
    migration: number;
    training: number;
    support: number;
  };
  providerComparison: {
    aws: number;
    azure: number;
    googleCloud: number;
  };
  recommendations: string[];
}

// Cost calculation constants based on industry standards
const COST_FACTORS = {
  serverMigrationBase: 5000, // Base cost per server
  dataTransferCost: 0.09, // Per GB
  trainingCostPerPerson: 2500,
  supportCostPercentage: 0.15, // 15% of total infrastructure cost
  
  // Provider multipliers
  providerMultipliers: {
    aws: 1.0,
    azure: 1.1,
    googleCloud: 1.05,
  },
  
  // Infrastructure type multipliers
  infrastructureMultipliers: {
    'on-premises': 1.0,
    'hybrid': 1.2,
    'multi-cloud': 1.4,
  },
  
  // Workload complexity multipliers
  workloadMultipliers: {
    databases: 1.5,
    applications: 1.2,
    storage: 0.8,
    networking: 1.1,
  },
  
  // Migration strategy multipliers
  strategyMultipliers: {
    'lift-and-shift': 1.0,
    'replatform': 1.3,
    'refactor': 1.8,
    'rebuild': 2.5,
  },
};

export function calculateMigrationCosts(assessmentData: any[]): CostCalculationResult {
  // Parse assessment data from the form structure
  const parsedData = parseAssessmentData(assessmentData);
  
  // Calculate base infrastructure costs
  const infrastructureCost = calculateInfrastructureCost(parsedData);
  
  // Calculate migration service costs
  const migrationCost = calculateMigrationServiceCost(parsedData);
  
  // Calculate training costs
  const trainingCost = calculateTrainingCost(parsedData);
  
  // Calculate ongoing support costs
  const supportCost = calculateSupportCost(infrastructureCost);
  
  const totalCost = infrastructureCost + migrationCost + trainingCost + supportCost;
  
  // Calculate monthly operational costs
  const monthlyOperationalCost = calculateMonthlyOperationalCost(parsedData);
  
  // Determine migration timeframe
  const migrationTimeframe = determineMigrationTimeframe(parsedData);
  
  // Recommend cloud provider
  const recommendedProvider = recommendCloudProvider(parsedData);
  
  // Calculate provider comparison
  const providerComparison = calculateProviderComparison(totalCost);
  
  // Generate recommendations
  const recommendations = generateRecommendations(parsedData, totalCost);
  
  return {
    summary: {
      totalCost: Math.round(totalCost),
      monthlyOperationalCost: Math.round(monthlyOperationalCost),
      migrationTimeframe,
      recommendedProvider,
      costRange: {
        lower: Math.round(totalCost * 0.8),
        upper: Math.round(totalCost * 1.3),
      },
    },
    breakdown: {
      infrastructure: Math.round(infrastructureCost),
      migration: Math.round(migrationCost),
      training: Math.round(trainingCost),
      support: Math.round(supportCost),
    },
    providerComparison,
    recommendations,
  };
}

function parseAssessmentData(assessmentData: any[]): any {
  // This function parses the form data structure into a more usable format
  // The assessmentData comes as an array of sections with questions and answers
  
  const parsed = {
    numberOfServers: 50, // Default values - these would be extracted from actual form data
    serverCapacity: 150,
    currentMonthlyCost: 25000,
    workloadTypes: ['databases', 'applications'],
    migrationStrategy: 'lift-and-shift',
    cloudProvider: 'aws',
    highAvailability: true,
    complianceRequired: true,
  };
  
  // Extract actual values from assessmentData structure
  if (assessmentData && assessmentData.length > 0) {
    // Section 1: Business & Infrastructure
    if (assessmentData[0]) {
      // Parse server count, infrastructure type, etc.
    }
    
    // Section 2: Workload & Resource Analysis
    if (assessmentData[1]) {
      // Parse workload types, CPU usage, etc.
    }
    
    // Additional sections...
  }
  
  return parsed;
}

function calculateInfrastructureCost(data: any): number {
  const baseServerCost = data.numberOfServers * COST_FACTORS.serverMigrationBase;
  const capacityMultiplier = Math.max(1, data.serverCapacity / 100);
  const strategyMultiplier = COST_FACTORS.strategyMultipliers[data.migrationStrategy] || 1.0;
  
  return baseServerCost * capacityMultiplier * strategyMultiplier;
}

function calculateMigrationServiceCost(data: any): number {
  const baseMigrationCost = data.numberOfServers * 2000; // Base migration cost per server
  const complexityMultiplier = data.workloadTypes.length * 0.2 + 1;
  
  return baseMigrationCost * complexityMultiplier;
}

function calculateTrainingCost(data: any): number {
  const estimatedTeamSize = Math.max(5, Math.ceil(data.numberOfServers / 10));
  return estimatedTeamSize * COST_FACTORS.trainingCostPerPerson;
}

function calculateSupportCost(infrastructureCost: number): number {
  return infrastructureCost * COST_FACTORS.supportCostPercentage;
}

function calculateMonthlyOperationalCost(data: any): number {
  // Estimate monthly operational costs based on current costs and cloud efficiency
  const currentMonthlyCost = data.currentMonthlyCost || 25000;
  const efficiencyGain = 0.2; // Assume 20% efficiency gain
  const providerMultiplier = COST_FACTORS.providerMultipliers[data.cloudProvider] || 1.0;
  
  return currentMonthlyCost * (1 - efficiencyGain) * providerMultiplier;
}

function determineMigrationTimeframe(data: any): string {
  const serverCount = data.numberOfServers;
  const complexity = data.workloadTypes.length;
  
  if (serverCount <= 20 && complexity <= 2) {
    return '3-4 months';
  } else if (serverCount <= 50 && complexity <= 3) {
    return '4-6 months';
  } else if (serverCount <= 100) {
    return '6-8 months';
  } else {
    return '8-12 months';
  }
}

function recommendCloudProvider(data: any): string {
  // Simple recommendation logic - can be enhanced based on specific requirements
  if (data.complianceRequired && data.highAvailability) {
    return 'AWS';
  } else if (data.workloadTypes.includes('databases')) {
    return 'Azure';
  } else {
    return 'Google Cloud';
  }
}

function calculateProviderComparison(baseCost: number): any {
  return {
    aws: Math.round(baseCost * COST_FACTORS.providerMultipliers.aws),
    azure: Math.round(baseCost * COST_FACTORS.providerMultipliers.azure),
    googleCloud: Math.round(baseCost * COST_FACTORS.providerMultipliers.googleCloud),
  };
}

function generateRecommendations(data: any, totalCost: number): string[] {
  const recommendations = [];
  
  if (totalCost > 500000) {
    recommendations.push('Consider a phased migration approach to spread costs over time');
  }
  
  if (data.numberOfServers > 100) {
    recommendations.push('Implement automated migration tools to reduce manual effort');
  }
  
  if (data.migrationStrategy === 'lift-and-shift') {
    recommendations.push('Consider replatforming for better cloud optimization');
  }
  
  recommendations.push('Plan for 20-30% cost buffer for unexpected requirements');
  recommendations.push('Invest in team training early in the migration process');
  
  return recommendations;
}

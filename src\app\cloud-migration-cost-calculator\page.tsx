import { notFound } from 'next/navigation';
import CloudMigrationBody from '@components/CloudMigrationBody';
import seoSchema from '@utils/seoSchema';

import fetchFromStrapi from '@utils/fetchFromStrapi';
import RichResults from '@components/RichResults';

async function getCloudMigrationData() {
  return await fetchFromStrapi(
    'cloud-migration-cost-calculator',
    'populate=hero_section.image,cloud_migration_components.question.answers,cloud_migration_components.question.sub_question,form.formFields,form.button,restart_button,consultation_button,tag_list,tag,seo.schema',
  );
}

async function getFormData() {
  const query = `populate=form.formFields&populate=form.button`;
  return await fetchFromStrapi('form', query);
}

export async function generateMetadata() {
  const cloudMigrationData = await getCloudMigrationData();
  const seo = cloudMigrationData?.data?.attributes?.seo;

  return seoSchema(seo);
}

export default async function CloudMigrationCostCalculator() {
  const cloudMigrationData = await getCloudMigrationData();
  const formData = await getFormData();

  if (!cloudMigrationData?.data || cloudMigrationData?.data.length === 0) {
    notFound();
  }

  return (
    <>
      {cloudMigrationData?.data?.attributes?.seo && (
        <RichResults data={cloudMigrationData?.data?.attributes?.seo} />
      )}
      {cloudMigrationData?.data?.attributes && (
        <CloudMigrationBody
          body={cloudMigrationData?.data?.attributes}
          formData={formData?.data?.attributes?.form}
        />
      )}
    </>
  );
}

@value variables: "@styles/variables.module.css";
@value brandColorOne, brandColorTwo, brandColorThree, brandColorFour, brandColorFive, colorWhite, colorBlack, gray300 from variables;
@value breakpoints: "@styles/breakpoints.module.css";
@value breakpoint-sm, breakpoint-xl, breakpoint-xl-1024, breakpoint-xl-1440 from breakpoints;

.form_container {
  max-width: 600px;
  margin: 0 auto;
}

.form_header {
  text-align: center;
  margin-bottom: 32px;
}

.form_header h3 {
  font-size: 24px;
  font-weight: 700;
  color: colorBlack;
  margin: 0 0 12px 0;
}

.instructions {
  font-size: 16px;
  color: #666;
  line-height: 1.5;
  margin: 0;
}

.form {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.form_row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;

  @media screen and (max-width: breakpoint-xl-1024) {
    grid-template-columns: 1fr;
    gap: 16px;
  }
}

.form_group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.label {
  font-size: 14px;
  font-weight: 600;
  color: colorBlack;
}

.input,
.textarea {
  padding: 12px 16px;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  font-size: 16px;
  line-height: 1.5;
  transition: all 0.3s ease;
  background-color: colorWhite;
}

.input:focus,
.textarea:focus {
  outline: none;
  border-color: brandColorOne;
  box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.1);
}

.input.error,
.textarea.error {
  border-color: #ff4444;
  background-color: #fff5f5;
}

.textarea {
  resize: vertical;
  min-height: 100px;
  font-family: inherit;
}

.error_message {
  padding: 12px 16px;
  background-color: #fff5f5;
  border: 1px solid #ffcccc;
  border-radius: 8px;
  color: #ff4444;
  font-size: 14px;
}

.error_message p {
  margin: 0;
}

.consent_row {
  margin: 16px 0;
}

.consent_label {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  font-size: 14px;
  line-height: 1.5;
  color: #666;
  cursor: pointer;
}

.consent_label.error {
  color: #ff4444;
}

.consent_label input[type="checkbox"] {
  width: 18px;
  height: 18px;
  accent-color: brandColorOne;
  margin-top: 2px;
  flex-shrink: 0;
}

.submit_button_row {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  margin-top: 24px;
}

.submit_button {
  padding: 16px 32px;
  background-color: brandColorOne;
  color: colorWhite;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 250px;
}

.submit_button:hover {
  background-color: brandColorTwo;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(74, 144, 226, 0.3);
}

.linkedin_button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  background-color: #0077b5;
  color: colorWhite;
  text-decoration: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.linkedin_button:hover {
  background-color: #005885;
  transform: translateY(-2px);
}

.container_spinner {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 16px;
}

.spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #e9ecef;
  border-top: 3px solid brandColorOne;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@media screen and (max-width: breakpoint-xl-1024) {
  .form_container {
    padding: 0 16px;
  }
  
  .form_header h3 {
    font-size: 20px;
  }
  
  .instructions {
    font-size: 14px;
  }
  
  .form {
    gap: 20px;
  }
  
  .input,
  .textarea {
    padding: 10px 14px;
    font-size: 14px;
  }
  
  .submit_button {
    padding: 14px 28px;
    font-size: 14px;
    min-width: 200px;
  }
  
  .linkedin_button {
    padding: 10px 20px;
    font-size: 12px;
  }
}

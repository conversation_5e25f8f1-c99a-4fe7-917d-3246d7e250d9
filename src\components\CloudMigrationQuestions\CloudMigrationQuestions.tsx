'use client';

import { useState } from 'react';
import styles from './CloudMigrationQuestions.module.css';
import useMediaQueryState from '@hooks/useMediaQueryState';
import breakpoints from '@styles/breakpoints.module.css';

export default function CloudMigrationQuestions({
  sectionIndex,
  sectionQuestions,
  sectionData,
  sectionError,
  handleData,
  handleError,
}) {
  const isTablet = useMediaQueryState({
    query: `(max-width: ${breakpoints['breakpoint-xl-1024']})`,
  });

  const [subAnswer, setSubAnswer] = useState(() => {
    if (
      typeof window !== 'undefined' &&
      localStorage.getItem('cloudMigrationSubAnswer') !== null
    ) {
      return JSON.parse(localStorage.getItem('cloudMigrationSubAnswer'));
    }
    return [[], new Array(10).fill(null)];
  });

  // Keyboard event handler
  const handleKeyDown = (event, callback) => {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      callback();
    }
  };

  function handleID(
    subQuestionIndex,
    answerName,
    answerId,
    sectionIndex,
    questionIndex,
  ) {
    const newSubAnswer = [...subAnswer];
    newSubAnswer[1][subQuestionIndex] = answerId;
    setSubAnswer(newSubAnswer);

    if (typeof window !== 'undefined') {
      localStorage.setItem(
        'cloudMigrationSubAnswer',
        JSON.stringify(newSubAnswer),
      );
    }

    handleError(sectionIndex, questionIndex, false);
    handleData(sectionIndex, questionIndex, answerName, answerId);
  }

  function setSubQuestion(question, sectionIndex, questionIndex) {
    const { sub_question, answers } = question;

    return sub_question.map((subQ, i) => {
      const subQuestionAnswers = answers.filter(
        ans => ans.sub_question_id === subQ.id,
      );

      return (
        <div key={subQ.id} className={styles.sub_question_container}>
          <div className={styles.sub_question_title}>{subQ.name}</div>
          <div className={styles.sub_question_answers}>
            {subQuestionAnswers.map((answer, answerIndex) => {
              const currentAnswerIndex = answerIndex;
              const currentAnswer = subQuestionAnswers[currentAnswerIndex];

              return (
                <label
                  key={currentAnswer.id}
                  className={
                    subAnswer[1][i] === currentAnswer.id
                      ? `${styles.mcq} ${styles.selected_mcq}`
                      : styles.mcq
                  }
                  htmlFor={currentAnswer.id}
                  tabIndex={0}
                  onKeyDown={e =>
                    handleKeyDown(e, () => {
                      handleID(
                        i,
                        currentAnswer.name,
                        Number(currentAnswer.id),
                        sectionIndex,
                        questionIndex,
                      );
                    })
                  }
                >
                  <input
                    type="radio"
                    id={currentAnswer.id}
                    name={subQ.name}
                    data-name={currentAnswer.name}
                    value={currentAnswer.id}
                    onChange={e => {
                      handleID(
                        i,
                        e.target.dataset.name,
                        Number(e.target.value),
                        sectionIndex,
                        questionIndex,
                      );
                    }}
                  />
                  {currentAnswer.name}
                </label>
              );
            })}
          </div>
        </div>
      );
    });
  }

  return (
    <div className={styles.questions_container}>
      {sectionQuestions.map((question, questionIndex) => (
        <div
          key={questionIndex}
          className={
            sectionError[questionIndex]
              ? `${styles.question_wrapper} ${styles.error}`
              : styles.question_wrapper
          }
        >
          <div className={styles.question_header}>
            <div
              className={
                sectionError[questionIndex]
                  ? `${styles.question_number} ${styles.error_number}`
                  : styles.question_number
              }
            >
              {question.number < 10 ? '0' : ''}
              {question.number}
              {'.'}
            </div>
            <div className={styles.question_name}>{question.name}</div>
          </div>

          {question.type === 'mcq' ? (
            <div className={styles.mcqs_container}>
              {question.sub_question && question.sub_question.length !== 0 ? (
                <>{setSubQuestion(question, sectionIndex, questionIndex)}</>
              ) : (
                <>
                  {question.answers.map((ans, answerIndex) => (
                    <label
                      key={answerIndex}
                      className={
                        sectionData[questionIndex] &&
                        sectionData[questionIndex][1] === ans.value
                          ? `${styles.mcq} ${styles.selected_mcq}`
                          : styles.mcq
                      }
                      htmlFor={ans.id}
                      tabIndex={0}
                      onKeyDown={e =>
                        handleKeyDown(e, () => {
                          handleError(sectionIndex, questionIndex, false);
                          handleData(
                            sectionIndex,
                            questionIndex,
                            ans.name,
                            Number(ans.value),
                          );
                        })
                      }
                    >
                      <input
                        type="radio"
                        id={ans.id}
                        name={question.name}
                        data-name={ans.name}
                        value={ans.value}
                        onChange={e => {
                          handleError(sectionIndex, questionIndex, false);
                          handleData(
                            sectionIndex,
                            questionIndex,
                            e.target.dataset.name,
                            Number(e.target.value),
                          );
                        }}
                      />
                      {ans.name}
                    </label>
                  ))}
                </>
              )}
            </div>
          ) : question.type === 'input' ? (
            <div className={styles.input_container}>
              <input
                type="text"
                className={styles.text_input}
                placeholder={question.placeholder || 'Enter your answer...'}
                value={
                  sectionData[questionIndex]
                    ? sectionData[questionIndex][0]
                    : ''
                }
                onChange={e => {
                  handleError(sectionIndex, questionIndex, false);
                  handleData(
                    sectionIndex,
                    questionIndex,
                    e.target.value,
                    e.target.value,
                  );
                }}
              />
            </div>
          ) : question.type === 'number' ? (
            <div className={styles.input_container}>
              <input
                type="number"
                className={styles.number_input}
                placeholder={question.placeholder || 'Enter number...'}
                value={
                  sectionData[questionIndex]
                    ? sectionData[questionIndex][1]
                    : ''
                }
                onChange={e => {
                  handleError(sectionIndex, questionIndex, false);
                  handleData(
                    sectionIndex,
                    questionIndex,
                    e.target.value,
                    Number(e.target.value),
                  );
                }}
              />
            </div>
          ) : question.type === 'select' ? (
            <div className={styles.select_container}>
              <select
                className={styles.select_input}
                value={
                  sectionData[questionIndex]
                    ? sectionData[questionIndex][1]
                    : ''
                }
                onChange={e => {
                  const selectedOption = question.answers.find(
                    ans => ans.value === Number(e.target.value),
                  );
                  handleError(sectionIndex, questionIndex, false);
                  handleData(
                    sectionIndex,
                    questionIndex,
                    selectedOption?.name || e.target.value,
                    Number(e.target.value),
                  );
                }}
              >
                <option value="">Select an option...</option>
                {question.answers.map((ans, answerIndex) => (
                  <option key={answerIndex} value={ans.value}>
                    {ans.name}
                  </option>
                ))}
              </select>
            </div>
          ) : null}
        </div>
      ))}
    </div>
  );
}

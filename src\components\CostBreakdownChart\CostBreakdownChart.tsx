'use client';

import { useState } from 'react';
import styles from './CostBreakdownChart.module.css';

export default function CostBreakdownChart({ 
  totalCost, 
  breakdown, 
  recommendedProvider 
}) {
  const [activeTab, setActiveTab] = useState('breakdown');

  const breakdownItems = [
    { 
      label: 'Infrastructure Migration', 
      value: breakdown.infrastructure || 0, 
      color: '#4A90E2',
      description: 'Server migration, network setup, storage configuration'
    },
    { 
      label: 'Migration Services', 
      value: breakdown.migration || 0, 
      color: '#7ED321',
      description: 'Data transfer, application migration, testing'
    },
    { 
      label: 'Training & Support', 
      value: breakdown.training || 0, 
      color: '#F5A623',
      description: 'Team training, documentation, knowledge transfer'
    },
    { 
      label: 'Ongoing Support', 
      value: breakdown.support || 0, 
      color: '#BD10E0',
      description: 'Post-migration support, monitoring, optimization'
    }
  ];

  const getPercentage = (value) => {
    return totalCost > 0 ? ((value / totalCost) * 100).toFixed(1) : 0;
  };

  const providerComparison = [
    {
      provider: 'AWS',
      cost: totalCost,
      savings: '15-20%',
      features: ['Comprehensive services', 'Global infrastructure', 'Enterprise support'],
      recommended: recommendedProvider === 'AWS'
    },
    {
      provider: 'Azure',
      cost: Math.round(totalCost * 1.1),
      savings: '10-15%',
      features: ['Microsoft integration', 'Hybrid capabilities', 'AI/ML services'],
      recommended: recommendedProvider === 'Azure'
    },
    {
      provider: 'Google Cloud',
      cost: Math.round(totalCost * 1.05),
      savings: '12-18%',
      features: ['Data analytics', 'Kubernetes native', 'Competitive pricing'],
      recommended: recommendedProvider === 'Google Cloud'
    }
  ];

  return (
    <div className={styles.chart_container}>
      <div className={styles.tab_navigation}>
        <button
          className={activeTab === 'breakdown' ? `${styles.tab} ${styles.active}` : styles.tab}
          onClick={() => setActiveTab('breakdown')}
        >
          Cost Breakdown
        </button>
        <button
          className={activeTab === 'comparison' ? `${styles.tab} ${styles.active}` : styles.tab}
          onClick={() => setActiveTab('comparison')}
        >
          Provider Comparison
        </button>
      </div>

      {activeTab === 'breakdown' && (
        <div className={styles.breakdown_content}>
          <div className={styles.total_cost_display}>
            <h3>Total Migration Cost</h3>
            <div className={styles.total_amount}>
              ${totalCost?.toLocaleString()}
            </div>
          </div>

          <div className={styles.breakdown_chart}>
            <div className={styles.chart_visual}>
              {breakdownItems.map((item, index) => (
                <div
                  key={index}
                  className={styles.chart_segment}
                  style={{
                    width: `${getPercentage(item.value)}%`,
                    backgroundColor: item.color,
                    minWidth: '10px'
                  }}
                  title={`${item.label}: $${item.value.toLocaleString()} (${getPercentage(item.value)}%)`}
                />
              ))}
            </div>
            
            <div className={styles.chart_legend}>
              {breakdownItems.map((item, index) => (
                <div key={index} className={styles.legend_item}>
                  <div 
                    className={styles.legend_color}
                    style={{ backgroundColor: item.color }}
                  />
                  <div className={styles.legend_details}>
                    <div className={styles.legend_label}>{item.label}</div>
                    <div className={styles.legend_value}>
                      ${item.value.toLocaleString()} ({getPercentage(item.value)}%)
                    </div>
                    <div className={styles.legend_description}>{item.description}</div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {activeTab === 'comparison' && (
        <div className={styles.comparison_content}>
          <h3>Cloud Provider Comparison</h3>
          <div className={styles.provider_grid}>
            {providerComparison.map((provider, index) => (
              <div 
                key={index} 
                className={
                  provider.recommended 
                    ? `${styles.provider_card} ${styles.recommended}` 
                    : styles.provider_card
                }
              >
                {provider.recommended && (
                  <div className={styles.recommended_badge}>Recommended</div>
                )}
                <div className={styles.provider_header}>
                  <h4>{provider.provider}</h4>
                  <div className={styles.provider_cost}>
                    ${provider.cost.toLocaleString()}
                  </div>
                </div>
                <div className={styles.provider_savings}>
                  Potential Savings: {provider.savings}
                </div>
                <div className={styles.provider_features}>
                  <h5>Key Features:</h5>
                  <ul>
                    {provider.features.map((feature, featureIndex) => (
                      <li key={featureIndex}>{feature}</li>
                    ))}
                  </ul>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}

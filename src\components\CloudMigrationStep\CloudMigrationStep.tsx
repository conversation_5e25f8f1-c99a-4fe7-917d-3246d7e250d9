'use client';

import styles from './CloudMigrationStep.module.css';

export default function CloudMigrationStep({ currentStep, totalSteps }) {
  return (
    <div className={styles.step_container}>
      <div className={styles.step_indicator}>
        <span className={styles.step_text}>
          Step {currentStep} of {totalSteps}
        </span>
        <div className={styles.progress_bar}>
          <div 
            className={styles.progress_fill}
            style={{ width: `${(currentStep / totalSteps) * 100}%` }}
          />
        </div>
      </div>
    </div>
  );
}

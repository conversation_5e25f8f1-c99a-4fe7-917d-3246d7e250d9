@value variables: "@styles/variables.module.css";
@value brandColorOne, brandColorTwo, brandColorThree, brandColorFour, brandColorFive, colorWhite, colorBlack, gray300 from variables;
@value breakpoints: "@styles/breakpoints.module.css";
@value breakpoint-sm, breakpoint-xl, breakpoint-xl-1024, breakpoint-xl-1440 from breakpoints;

.container {
  padding: 36px 0 80px 0;

  @media screen and (max-width: breakpoint-xl-1024) {
    padding: 36px 0 36px 0;
  }
}

.step_container {
  height: 45px;
  padding: 16px;
  display: flex;
  gap: 10px;

  font-weight: 500;
  font-size: 18px;
  line-height: 160%;
  letter-spacing: 0.36;
}

.hidden {
  display: none;
}

.section_wrapper {
  width: fit-content;
  display: flex;
  flex-direction: column;
  gap: 40px;
  margin: 0 auto;
  padding: 0 10px;

  @media screen and (min-width: 1200px) {
    width: 1192px;
  }
}

.heading > h2 {
  font-weight: 600;
  font-size: 32px;
  line-height: 132%;
  padding: 20px;
  border-bottom: 2px solid black;
}

.error_message {
  color: #ff4444;
  font-size: 14px;
  font-weight: 500;
  margin-top: 10px;
  padding: 10px;
  background-color: #fff5f5;
  border: 1px solid #ffcccc;
  border-radius: 4px;
}

.button_container {
  display: flex;
  gap: 20px;
  justify-content: center;
  margin-top: 40px;

  @media screen and (max-width: breakpoint-xl-1024) {
    flex-direction: column;
    align-items: center;
  }
}

.previous_button,
.next_button,
.calculate_button {
  padding: 12px 24px;
  font-size: 16px;
  font-weight: 600;
  border-radius: 8px;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 150px;
}

.previous_button {
  background-color: gray300;
  color: colorBlack;
}

.previous_button:hover {
  background-color: #e0e0e0;
}

.next_button,
.calculate_button {
  background-color: brandColorOne;
  color: colorWhite;
}

.next_button:hover,
.calculate_button:hover {
  background-color: brandColorTwo;
}

.results_container {
  padding: 60px 0;
  background-color: #f8f9fa;
}

.results_wrapper {
  width: fit-content;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: flex;
  flex-direction: column;
  gap: 40px;

  @media screen and (min-width: 1200px) {
    width: 1192px;
  }
}

.cost_summary {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin: 30px 0;
}

.cost_item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  background-color: colorWhite;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.cost_label {
  font-weight: 500;
  color: #666;
}

.cost_value {
  font-weight: 700;
  font-size: 18px;
  color: brandColorOne;
}

.form_section {
  background-color: colorWhite;
  padding: 40px;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.form_heading {
  text-align: center;
  margin-bottom: 30px;
}

.restart_section {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.restart_button {
  padding: 12px 24px;
  background-color: #6c757d;
  color: colorWhite;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.restart_button:hover {
  background-color: #5a6268;
}

.hero_section {
  margin-bottom: 40px;
}

@media screen and (max-width: breakpoint-xl-1024) {
  .section_wrapper {
    padding: 0 20px;
  }
  
  .results_wrapper {
    padding: 0 20px;
  }
  
  .form_section {
    padding: 20px;
  }
  
  .cost_summary {
    grid-template-columns: 1fr;
  }
  
  .cost_item {
    flex-direction: column;
    gap: 10px;
    text-align: center;
  }
}

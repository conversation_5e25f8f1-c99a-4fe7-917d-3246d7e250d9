'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import styles from './CloudMigrationBody.module.css';
import useMediaQueryState from '@hooks/useMediaQueryState';

import CloudMigrationQuestions from '@components/CloudMigrationQuestions';
import Heading from '@components/Heading';
import Button from '@components/Button';
import HeroSection from '@components/HeroSection';
import CloudMigrationStep from '@components/CloudMigrationStep';
import CostBreakdownChart from '@components/CostBreakdownChart';
import CloudMigrationForm from '@components/CloudMigrationForm';

import breakpoints from '@styles/breakpoints.module.css';

export default function CloudMigrationBody({ body, formData }) {
  const router = useRouter();
  const isTablet = useMediaQueryState({
    query: `(max-width: ${breakpoints['breakpoint-xl-1024']})`,
  });

  const [data, setData] = useState(() => {
    if (
      typeof window !== 'undefined' &&
      localStorage.getItem('cloudMigrationData') !== null
    ) {
      return JSON.parse(localStorage.getItem('cloudMigrationData'));
    }
    return body?.cloud_migration_components?.data.map(() => []);
  });

  const [error, setError] = useState(() => {
    if (
      typeof window !== 'undefined' &&
      localStorage.getItem('cloudMigrationError') !== null
    ) {
      return JSON.parse(localStorage.getItem('cloudMigrationError'));
    }
    return body?.cloud_migration_components?.data.map(() => []);
  });

  const [visibleSection, setVisibleSection] = useState(() => {
    if (
      typeof window !== 'undefined' &&
      localStorage.getItem('cloudMigrationVisibleSection') !== null
    ) {
      return JSON.parse(localStorage.getItem('cloudMigrationVisibleSection'));
    }
    return 0;
  });

  const [result, setResult] = useState(() => {
    if (
      typeof window !== 'undefined' &&
      localStorage.getItem('cloudMigrationResult') !== null
    ) {
      return JSON.parse(localStorage.getItem('cloudMigrationResult'));
    }
    return {};
  });

  const [costBreakdown, setCostBreakdown] = useState(() => {
    if (
      typeof window !== 'undefined' &&
      localStorage.getItem('cloudMigrationCostBreakdown') !== null
    ) {
      return JSON.parse(localStorage.getItem('cloudMigrationCostBreakdown'));
    }
    return {};
  });

  useEffect(() => {
    if (typeof window !== 'undefined') {
      localStorage.setItem('cloudMigrationData', JSON.stringify(data));
    }
  }, [data]);

  useEffect(() => {
    if (typeof window !== 'undefined') {
      localStorage.setItem('cloudMigrationError', JSON.stringify(error));
    }
  }, [error]);

  useEffect(() => {
    if (typeof window !== 'undefined') {
      localStorage.setItem(
        'cloudMigrationVisibleSection',
        JSON.stringify(visibleSection),
      );
    }
  }, [visibleSection]);

  useEffect(() => {
    if (typeof window !== 'undefined') {
      localStorage.setItem('cloudMigrationResult', JSON.stringify(result));
    }
  }, [result]);

  useEffect(() => {
    if (typeof window !== 'undefined') {
      localStorage.setItem(
        'cloudMigrationCostBreakdown',
        JSON.stringify(costBreakdown),
      );
    }
  }, [costBreakdown]);

  function handleData(sectionIndex, questionIndex, answerName, answerValue) {
    const newData = [...data];
    newData[sectionIndex][questionIndex] = [answerName, answerValue];
    setData(newData);
  }

  function handleError(sectionIndex, questionIndex, hasError) {
    const newError = [...error];
    newError[sectionIndex][questionIndex] = hasError;
    setError(newError);
  }

  function canGoToNext() {
    if (visibleSection < data.length) {
      const currentSectionData = data[visibleSection];
      const currentSectionQuestions =
        body?.cloud_migration_components?.data[visibleSection]?.attributes
          ?.question;

      return (
        currentSectionData.length === currentSectionQuestions.length &&
        !currentSectionData.some(item => !item || item.length === 0)
      );
    }
    return false;
  }

  function handleNext() {
    if (canGoToNext()) {
      setVisibleSection(visibleSection + 1);
    } else {
      const newError = [...error];
      const currentSectionQuestions =
        body?.cloud_migration_components?.data[visibleSection]?.attributes
          ?.question;

      for (let i = 0; i < currentSectionQuestions.length; i++) {
        if (!data[visibleSection][i] || data[visibleSection][i].length === 0) {
          newError[visibleSection][i] = true;
        }
      }
      setError(newError);
    }
  }

  function handlePrevious() {
    if (visibleSection > 0) {
      setVisibleSection(visibleSection - 1);
    }
  }

  function handleRestart() {
    setData(body?.cloud_migration_components?.data.map(() => []));
    setError(body?.cloud_migration_components?.data.map(() => []));
    setVisibleSection(0);
    setResult({});
    setCostBreakdown({});

    if (typeof window !== 'undefined') {
      localStorage.removeItem('cloudMigrationData');
      localStorage.removeItem('cloudMigrationError');
      localStorage.removeItem('cloudMigrationVisibleSection');
      localStorage.removeItem('cloudMigrationResult');
      localStorage.removeItem('cloudMigrationCostBreakdown');
    }
  }

  function calculateCosts() {
    if (canGoToNext()) {
      const calculationResult = performCostCalculation(data);
      setResult(calculationResult.summary);
      setCostBreakdown(calculationResult.breakdown);
      return { data, calculationResult };
    }
  }

  function performCostCalculation(assessmentData) {
    // Import the calculation function dynamically to avoid SSR issues
    const {
      calculateMigrationCosts,
    } = require('@utils/cloudMigrationCalculator');
    return calculateMigrationCosts(assessmentData);
  }

  const tag_list = body?.tag_list || [];
  const tag_colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7'];

  return (
    <>
      {body?.hero_section && (
        <HeroSection
          heroSection={body.hero_section}
          className={styles.hero_section}
        />
      )}

      {visibleSection < data.length && (
        <div className={styles.container}>
          <div className={styles.step_container}>
            <CloudMigrationStep
              currentStep={visibleSection + 1}
              totalSteps={data.length}
            />
          </div>

          {body?.cloud_migration_components?.data.map(
            (section, sectionIndex) => (
              <div
                key={sectionIndex}
                className={
                  visibleSection === sectionIndex
                    ? styles.section_wrapper
                    : styles.hidden
                }
              >
                <div className={styles.heading}>
                  <h2>
                    {sectionIndex + 1}. {section?.attributes?.heading}
                  </h2>
                </div>
                {visibleSection !== data.length && (
                  <CloudMigrationQuestions
                    sectionIndex={sectionIndex}
                    sectionQuestions={section?.attributes.question}
                    sectionData={data[sectionIndex]}
                    sectionError={error[sectionIndex]}
                    handleData={handleData}
                    handleError={handleError}
                  />
                )}
                <span id="error">
                  {visibleSection < data.length &&
                    error[visibleSection].includes(true) && (
                      <div className={styles.error_message}>
                        Please fill all the required fields.
                      </div>
                    )}
                </span>

                <div className={styles.button_container}>
                  {visibleSection > 0 && (
                    <Button
                      className={styles.previous_button}
                      label="Previous"
                      type="button"
                      onClick={handlePrevious}
                    />
                  )}

                  {visibleSection < data.length - 1 ? (
                    <Button
                      className={styles.next_button}
                      label="Next"
                      type="button"
                      onClick={handleNext}
                    />
                  ) : (
                    <Button
                      className={styles.calculate_button}
                      label="Calculate Migration Costs"
                      type="button"
                      onClick={() => {
                        const calculationResult = calculateCosts();
                        if (calculationResult) {
                          setVisibleSection(data.length);
                        }
                      }}
                    />
                  )}
                </div>
              </div>
            ),
          )}
        </div>
      )}

      {visibleSection === data.length && Object.keys(result).length > 0 && (
        <div className={styles.results_container}>
          <div className={styles.results_wrapper}>
            <div className={styles.heading}>
              <h2>Your Cloud Migration Cost Estimate</h2>
            </div>

            <CostBreakdownChart
              totalCost={result.totalCost}
              breakdown={costBreakdown}
              recommendedProvider={result.recommendedProvider}
            />

            <div className={styles.cost_summary}>
              <div className={styles.cost_item}>
                <span className={styles.cost_label}>Total Migration Cost:</span>
                <span className={styles.cost_value}>
                  ${result.totalCost?.toLocaleString()}
                </span>
              </div>
              <div className={styles.cost_item}>
                <span className={styles.cost_label}>
                  Monthly Operational Cost:
                </span>
                <span className={styles.cost_value}>
                  ${result.monthlyOperationalCost?.toLocaleString()}
                </span>
              </div>
              <div className={styles.cost_item}>
                <span className={styles.cost_label}>Estimated Timeframe:</span>
                <span className={styles.cost_value}>
                  {result.migrationTimeframe}
                </span>
              </div>
            </div>

            <div className={styles.form_section}>
              <Heading
                title="Get Your Detailed Migration Plan"
                headingType="h3"
                className={styles.form_heading}
              />
              <CloudMigrationForm
                formData={formData}
                source="CloudMigration"
                handleResult={calculateCosts}
                handleVisibleSection={setVisibleSection}
              />
            </div>

            <div className={styles.restart_section}>
              <Button
                className={styles.restart_button}
                label={body?.restart_button?.title || 'Start Over'}
                type="button"
                onClick={handleRestart}
              />
            </div>
          </div>
        </div>
      )}
    </>
  );
}

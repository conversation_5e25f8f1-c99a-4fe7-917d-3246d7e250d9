@value variables: "@styles/variables.module.css";
@value brandColorOne, brandColorTwo, brandColorThree, brandColorFour, brandColorFive, colorWhite, colorBlack, gray300 from variables;
@value breakpoints: "@styles/breakpoints.module.css";
@value breakpoint-sm, breakpoint-xl, breakpoint-xl-1024, breakpoint-xl-1440 from breakpoints;

.step_container {
  display: flex;
  justify-content: center;
  margin-bottom: 40px;
}

.step_indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  width: 100%;
  max-width: 400px;
}

.step_text {
  font-weight: 600;
  font-size: 16px;
  color: colorBlack;
  text-align: center;
}

.progress_bar {
  width: 100%;
  height: 8px;
  background-color: #e9ecef;
  border-radius: 4px;
  overflow: hidden;
}

.progress_fill {
  height: 100%;
  background: linear-gradient(90deg, brandColorOne 0%, brandColorTwo 100%);
  border-radius: 4px;
  transition: width 0.3s ease;
}

@media screen and (max-width: breakpoint-xl-1024) {
  .step_indicator {
    max-width: 300px;
  }
  
  .step_text {
    font-size: 14px;
  }
  
  .progress_bar {
    height: 6px;
  }
}
